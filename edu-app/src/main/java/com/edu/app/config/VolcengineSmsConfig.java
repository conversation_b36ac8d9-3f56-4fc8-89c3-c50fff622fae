package com.edu.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 火山引擎短信服务配置
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "volcengine.sms")
public class VolcengineSmsConfig {
    
    /**
     * Access Key ID
     */
    private String accessKeyId;
    
    /**
     * Secret Access Key
     */
    private String secretAccessKey;
    
    /**
     * 服务地区
     */
    private String region = "cn-north-1";
    
    /**
     * 服务名称
     */
    private String service = "sms";
    
    /**
     * API版本
     */
    private String version = "2021-01-11";
    
    /**
     * 短信签名
     */
    private String signName;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * API端点
     */
    private String endpoint = "https://sms.volcengineapi.com";
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 5000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 10000;
}

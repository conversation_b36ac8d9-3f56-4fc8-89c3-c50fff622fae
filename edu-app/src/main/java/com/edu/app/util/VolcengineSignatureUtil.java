package com.edu.app.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 火山引擎API签名工具类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class VolcengineSignatureUtil {
    
    private static final String ALGORITHM = "AWS4-HMAC-SHA256";
    private static final String TERMINATOR = "aws4_request";
    
    /**
     * 生成签名
     */
    public static String generateSignature(String accessKeyId, String secretAccessKey, 
                                         String region, String service, String method,
                                         String uri, Map<String, String> queryParams,
                                         Map<String, String> headers, String payload) {
        try {
            // 1. 创建规范请求
            String canonicalRequest = createCanonicalRequest(method, uri, queryParams, headers, payload);
            
            // 2. 创建待签名字符串
            Date now = new Date();
            String dateStamp = new SimpleDateFormat("yyyyMMdd").format(now);
            String amzDate = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'").format(now);
            
            String credentialScope = dateStamp + "/" + region + "/" + service + "/" + TERMINATOR;
            String stringToSign = ALGORITHM + "\n" + amzDate + "\n" + credentialScope + "\n" + 
                                sha256Hex(canonicalRequest);
            
            // 3. 计算签名
            byte[] signingKey = getSignatureKey(secretAccessKey, dateStamp, region, service);
            String signature = bytesToHex(hmacSha256(stringToSign, signingKey));
            
            // 4. 创建Authorization头
            String authorization = ALGORITHM + " " +
                                 "Credential=" + accessKeyId + "/" + credentialScope + ", " +
                                 "SignedHeaders=" + getSignedHeaders(headers) + ", " +
                                 "Signature=" + signature;
            
            return authorization;
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }
    
    /**
     * 创建规范请求
     */
    private static String createCanonicalRequest(String method, String uri, 
                                               Map<String, String> queryParams,
                                               Map<String, String> headers, String payload) {
        StringBuilder canonicalRequest = new StringBuilder();
        
        // HTTP方法
        canonicalRequest.append(method).append("\n");
        
        // 规范URI
        canonicalRequest.append(uri).append("\n");
        
        // 规范查询字符串
        canonicalRequest.append(getCanonicalQueryString(queryParams)).append("\n");
        
        // 规范头部
        canonicalRequest.append(getCanonicalHeaders(headers)).append("\n");
        
        // 签名头部
        canonicalRequest.append(getSignedHeaders(headers)).append("\n");
        
        // 负载哈希
        canonicalRequest.append(sha256Hex(payload));
        
        return canonicalRequest.toString();
    }
    
    /**
     * 获取规范查询字符串
     */
    private static String getCanonicalQueryString(Map<String, String> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return "";
        }
        
        List<String> sortedKeys = new ArrayList<>(queryParams.keySet());
        Collections.sort(sortedKeys);
        
        StringBuilder queryString = new StringBuilder();
        for (String key : sortedKeys) {
            if (queryString.length() > 0) {
                queryString.append("&");
            }
            queryString.append(urlEncode(key)).append("=").append(urlEncode(queryParams.get(key)));
        }
        
        return queryString.toString();
    }
    
    /**
     * 获取规范头部
     */
    private static String getCanonicalHeaders(Map<String, String> headers) {
        Map<String, String> sortedHeaders = new TreeMap<>();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            sortedHeaders.put(entry.getKey().toLowerCase(), entry.getValue().trim());
        }
        
        StringBuilder canonicalHeaders = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedHeaders.entrySet()) {
            canonicalHeaders.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        
        return canonicalHeaders.toString();
    }
    
    /**
     * 获取签名头部
     */
    private static String getSignedHeaders(Map<String, String> headers) {
        List<String> sortedKeys = new ArrayList<>();
        for (String key : headers.keySet()) {
            sortedKeys.add(key.toLowerCase());
        }
        Collections.sort(sortedKeys);
        
        return String.join(";", sortedKeys);
    }
    
    /**
     * 获取签名密钥
     */
    private static byte[] getSignatureKey(String key, String dateStamp, String regionName, String serviceName) {
        byte[] kDate = hmacSha256(dateStamp, ("AWS4" + key).getBytes(StandardCharsets.UTF_8));
        byte[] kRegion = hmacSha256(regionName, kDate);
        byte[] kService = hmacSha256(serviceName, kRegion);
        byte[] kSigning = hmacSha256(TERMINATOR, kService);
        return kSigning;
    }
    
    /**
     * HMAC-SHA256加密
     */
    private static byte[] hmacSha256(String data, byte[] key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException("HMAC-SHA256加密失败", e);
        }
    }
    
    /**
     * SHA256哈希
     */
    private static String sha256Hex(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException("SHA256哈希失败", e);
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * URL编码
     */
    private static String urlEncode(String value) {
        try {
            return URLEncoder.encode(value, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            throw new RuntimeException("URL编码失败", e);
        }
    }
}
